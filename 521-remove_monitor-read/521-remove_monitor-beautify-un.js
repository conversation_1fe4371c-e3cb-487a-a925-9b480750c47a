/*
 * VSCode Augment 插件反混淆分析
 *
 * 这是一个VSCode的Augment AI编程助手插件的核心文件
 * 经过混淆压缩，以下是反混淆分析结果
 */

// ============================================================================
// 核心工具函数和模块系统
// ============================================================================

// 对象创建和属性操作的简化引用
var createObject = Object.create,                    // zOt -> createObject
    defineProperty = Object.defineProperty,          // PB -> defineProperty
    getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor, // MOt -> getOwnPropertyDescriptor
    getOwnPropertyNames = Object.getOwnPropertyNames,           // FOt -> getOwnPropertyNames
    getPrototypeOf = Object.getPrototypeOf,                    // NOt -> getPrototypeOf
    hasOwnProperty = Object.prototype.hasOwnProperty,          // YOt -> hasOwnProperty

    // 模块缓存和延迟加载系统
    lazyEvaluator = (moduleFunc, cachedResult) => () => cachedResult = moduleFunc ? moduleFunc(moduleFunc = 0) : cachedResult, // ec -> lazyEvaluator

    // CommonJS模块包装器 - 创建模块导出对象
    moduleWrapper = (moduleFunc, exports) => () => (exports || moduleFunc((exports = {exports: {}}).exports, exports), exports.exports), // O -> moduleWrapper

    // 属性批量定义工具
    defineProperties = (target, properties) => {     // Yy -> defineProperties
        for (var key in properties) defineProperty(target, key, {
            get: properties[key],
            enumerable: true
        })
    },

    // 对象属性复制工具 - 用于模块导入/导出
    copyProperties = (target, source, excludeKey, descriptor) => {  // P7e -> copyProperties
        if (source && "object" == typeof source || "function" == typeof source)
            for (let key of getOwnPropertyNames(source))
                hasOwnProperty.call(target, key) || key === excludeKey ||
                defineProperty(target, key, {
                    get: () => source[key],
                    enumerable: !(descriptor = getOwnPropertyDescriptor(source, key)) || descriptor.enumerable
                });
        return target
    },

    // ES模块导入包装器
    moduleImporter = (module, isDefault, result) => (  // me -> moduleImporter
        result = null != module ? createObject(getPrototypeOf(module)) : {},
        copyProperties(!isDefault && module && module.__esModule ? result : defineProperty(result, "default", {
            value: module,
            enumerable: true
        }), module)
    ),

    // ES模块导出标记
    markAsESModule = module => copyProperties(defineProperty({}, "__esModule", {  // tc -> markAsESModule
        value: true
    }), module);

// ============================================================================
// Lodash工具函数模块
// ============================================================================

// 检查是否为对象类型
var isObject = moduleWrapper((exports, module) => {  // Eh -> isObject
    module.exports = function(value) {
        var type = typeof value;
        return null != value && ("object" == type || "function" == type)
    }
});

// 全局对象检测
var getGlobalObject = moduleWrapper((exports, module) => {  // Mre -> getGlobalObject
    var globalRef = "object" == typeof global && global && global.Object === Object && global;
    module.exports = globalRef
});

// 根对象获取 (global/self/window)
var getRootObject = moduleWrapper((exports, module) => {  // mp -> getRootObject
    var globalObj = getGlobalObject(),
        selfObj = "object" == typeof self && self && self.Object === Object && self,
        root = globalObj || selfObj || Function("return this")();
    module.exports = root
});

// 当前时间戳获取器
var getCurrentTimestamp = moduleWrapper((exports, module) => {  // owe -> getCurrentTimestamp
    var root = getRootObject();
    module.exports = function() {
        return root.Date.now()
    }
});

// 字符串尾部空白字符处理
var trimEndWhitespace = moduleWrapper((exports, module) => {  // lwe -> trimEndWhitespace
    var whitespaceRegex = /\s/;
    module.exports = function(string) {
        for (var index = string.length; index-- && whitespaceRegex.test(string.charAt(index)););
        return index
    }
});

// 字符串去除首尾空白
var trimString = moduleWrapper((exports, module) => {  // dwe -> trimString
    var trimEnd = trimEndWhitespace(),
        leadingWhitespace = /^\s+/;
    module.exports = function(string) {
        return string && string.slice(0, trimEnd(string) + 1).replace(leadingWhitespace, "")
    }
});

// Symbol引用获取
var getSymbol = moduleWrapper((exports, module) => {  // Uy -> getSymbol
    var symbolRef = getRootObject().Symbol;
    module.exports = symbolRef
});

// ============================================================================
// 类型检测和转换工具
// ============================================================================

// 获取对象的字符串标签 (用于类型检测)
var getObjectTag = moduleWrapper((exports, module) => {  // mwe -> getObjectTag
    var Symbol = getSymbol(),
        objectProto = Object.prototype,
        hasOwnProp = objectProto.hasOwnProperty,
        toString = objectProto.toString,
        toStringTag = Symbol ? Symbol.toStringTag : void 0;

    module.exports = function(value) {
        var isOwn = hasOwnProp.call(value, toStringTag),
            tag = value[toStringTag];
        try {
            var unmasked = !(value[toStringTag] = void 0)
        } catch {}
        var result = toString.call(value);
        return unmasked && (isOwn ? value[toStringTag] = tag : delete value[toStringTag]), result
    }
});

// 原生toString方法
var nativeToString = moduleWrapper((exports, module) => {  // Awe -> nativeToString
    var toString = Object.prototype.toString;
    module.exports = function(value) {
        return toString.call(value)
    }
});

// 基础类型标签获取器
var getBaseTag = moduleWrapper((exports, module) => {  // SA -> getBaseTag
    var Symbol = getSymbol(),
        getTag = getObjectTag(),
        nativeObjectToString = nativeToString(),
        toStringTag = Symbol ? Symbol.toStringTag : void 0;

    module.exports = function(value) {
        return null == value ?
            void 0 === value ? "[object Undefined]" : "[object Null]" :
            (toStringTag && toStringTag in Object(value) ? getTag : nativeObjectToString)(value)
    }
});

// ============================================================================
// 插件主要功能模块
// ============================================================================

/*
 * 从代码结构分析，这个插件主要包含以下核心功能：
 *
 * 1. AI代码补全和建议 (Augment AI completion)
 * 2. 聊天界面和交互 (Chat interface)
 * 3. 工作区管理 (Workspace management)
 * 4. 用户认证和会话管理 (Authentication & session)
 * 5. 配置管理 (Configuration management)
 * 6. 特性标志管理 (Feature flags)
 * 7. 文件编辑和同步 (File editing & sync)
 * 8. 模型管理 (AI model management)
 * 9. 调试和诊断功能 (Debug & diagnostics)
 * 10. 扩展生命周期管理 (Extension lifecycle)
 */

// 从文件末尾可以看到主要的激活函数和配置同步
// 主激活函数: qun (应该是 "activate" 的混淆名)
// 配置同步函数: fkt (应该是 "setupContextKeySync" 的混淆名)

// ============================================================================
// 关键配置项和上下文键
// ============================================================================

/*
 * 从代码中识别出的重要配置项：
 *
 * - vscode-augment.enableDebugFeatures: 启用调试功能
 * - vscode-augment.enableReviewerWorkflows: 启用审查工作流
 * - vscode-augment.enableNextEdit: 启用下一步编辑功能
 * - vscode-augment.enableGenerateCommitMessage: 启用生成提交消息
 * - vscode-augment.workspace-manager-ui.enabled: 工作区管理UI
 * - vscode-augment.sources-enabled: 启用源码功能
 * - vscode-augment.chat-hint.decoration: 聊天提示装饰
 * - vscode-augment.cpu-profile.enabled: CPU性能分析
 * - vscode-augment.featureFlags.enableRemoteAgents: 远程代理
 */

// ============================================================================
// URI处理和命令路由
// ============================================================================

/*
 * 插件支持的URI路径：
 *
 * - /auth/mcp: MCP OAuth回调处理
 * - S.authRedirectURI.path: 认证重定向
 * - zte.openChat: 打开聊天界面
 * - zte.openAugmentSettings: 打开Augment设置
 * - zte.openGuidelinesSettings: 打开指导原则设置
 * - zte.openMemories: 打开记忆文件
 */

// ============================================================================
// 核心类和主要功能模块分析
// ============================================================================

/*
 * 通过分析代码结构，发现这个插件的主要类和功能模块：
 */

// 主要的类定义 (从class关键字识别)
// 1. PS - 路径迭代器类，用于遍历文件系统路径
// 2. p8e - 不支持的文件扩展名错误类
// 3. nte - 文件扩展名过滤器基类
// 4. f8e - 带忽略路径映射的文件过滤器
// 5. h8e - 带忽略栈的文件过滤器
// 6. ite - 序列号生成器类
// 7. m8e - Blob名称计算器类
// 8. _8e - 文件变更监听器类
// 9. A8e - 文件上传处理器类
// 10. b8e - 可上传文件判断器类

// 工作区和版本控制相关类
// 11. cte - Git操作类，处理工作目录变更
// 12. lte - VCS仓库监听器，监控版本控制系统变更
// 13. ute - 仓库监听器管理器
// 14. dte - 开放文件管理器代理

// 数据存储和缓存相关类
// 15. pte - 简单键值存储类
// 16. fte - Blob状态存储类
// 17. C8e - 开放文档快照缓存类
// 18. hte - 文件编辑处理器类
// 19. x8e - 工作区资源管理器
// 20. k8e - 文件变更大小计数器

// 文件管理和同步相关类
// 21. mte - 开放文件管理器V2
// 22. KW - 工作队列项失败错误类
// 23. T8e - 错误抛出器类
// 24. R8e - 进度报告器类
// 25. D8e - 单项工作队列项
// 26. L8e - 批量工作队列项
// 27. _te - 基础队列类
// 28. O8e - 单项处理队列
// 29. W8e - 批量处理队列
// 30. Ate - 工作队列管理器

// 文件系统和路径管理
// 31. bte - 检查点管理器
// 32. B8e - 限制容器类
// 33. z8e - 项目计数限制器
// 34. vte - 磁盘文件管理器
// 35. T3 - 缓存文件名常量类
// 36. M8e - 修改时间缓存条目
// 37. yte - 修改时间缓存管理器

// 变更跟踪和文档管理
// 38. F8e - 文件修改记录类
// 39. QS - 变更序列类
// 40. qS - 变更跟踪器类
// 41. wte - 跟踪文档基类
// 42. N8e - 文本文档跟踪类
// 43. Y8e - 笔记本文档跟踪类
// 44. Cte - 开放文件管理器V1

// 路径和文件分类
// 45. Ste - 文件分类器类
// 46. kte - 路径映射管理器
// 47. P8e - 路径信息管理器
// 48. $8e - 路径错误类
// 49. Ite - 路径通知器类

// 工作区和同步管理
// 50. Tte - 文件验证器类
// 51. Rte - 固定大小列表类
// 52. Dte - 标签页监听器类
// 53. Lte - 未知Blob处理器类
// 54. Ote - 查看内容跟踪器类

// 核心工作区管理
// 55. US - 工作区状态类
// 56. V8e - 文件夹资源管理器
// 57. H8e - 路径过滤器基类
// 58. Wte - 工作区管理器主类 (核心类)
// 59. Bte - 动作模型状态管理器

// 主要的扩展类
// 60. U9 - 主扩展类 (核心扩展管理器)

// ============================================================================
// 主要功能函数分析
// ============================================================================

/*
 * 关键功能函数：
 */

// 工具函数
// - mx(): 生成随机字节数组，用于UUID生成
// - aBt(): UUID验证函数
// - kA(): UUID格式化函数
// - oBt(): UUID字符串化验证函数
// - cBt(): UUID v1生成函数
// - lBt(): UUID解析函数
// - uBt(): 字符串转字节数组
// - bx(): UUID命名空间处理函数
// - fBt(): MD5哈希函数
// - gBt(): UUID v4生成函数
// - mBt(): SHA1哈希函数
// - ABt(): UUID版本获取函数

// 类型系统和装饰器
// - FCe(): 类继承扩展函数
// - NCe(): 对象属性排除函数
// - YCe(): 装饰器应用函数
// - PCe(): 参数装饰器函数
// - RBt(): ES装饰器函数
// - DBt(): 初始化器运行函数
// - LBt(): 属性键处理函数
// - OBt(): 函数名设置函数

// 异步和生成器
// - $Ce(): 元数据装饰器函数
// - QCe(): 异步等待器函数
// - qCe(): 生成器函数
// - UCe(): 导出星号函数
// - pz(): 值迭代器函数
// - lne(): 读取函数
// - VCe(): 展开函数
// - HCe(): 展开数组函数
// - GCe(): 展开数组函数
// - Jy(): 等待包装器函数
// - jCe(): 异步生成器函数
// - KCe(): 异步委托器函数
// - JCe(): 异步值函数
// - XCe(): 模板对象创建函数
// - ZCe(): 导入星号函数
// - eSe(): 默认导入函数

// 私有成员访问
// - tSe(): 私有字段获取函数
// - rSe(): 私有字段设置函数
// - nSe(): 私有字段检查函数
// - sSe(): 可释放资源添加函数
// - iSe(): 资源释放函数

// 文件系统操作
// - Sy(): 事件等待函数
// - Eun(): 对象值查找函数
// - wun(): 差异变更类型获取函数
// - Cun(): 变更类型映射函数
// - Sun(): 删除路径处理函数
// - xun(): 添加路径处理函数
// - ote(): Git差异解析函数

// 路径和文件处理
// - Gxt(): 文件管理器版本检查函数
// - gte(): 文档文本获取函数
// - S8e(): 事件文档获取函数
// - $S(): 数组过滤函数
// - lA(): 日志输出函数
// - Dun(): 修改时间条目验证函数
// - Lun(): 缓存文件名生成函数
// - Ete(): 缓存存在检查函数
// - Jxt(): 缓存移动函数
// - Xxt(): 缓存读取函数

// 文档和变更跟踪
// - Yun(): 笔记本检查函数
// - Pun(): 文档包装函数
// - U8e(): URI路径获取函数
// - tB(): 文件URI路径获取函数
// - rB(): 同步阻塞检查函数
// - akt(): 嵌套检查函数
// - np(): 文件夹状态获取函数
// - skt(): 路径扫描函数

// 主要入口函数
// - j8e(): 会话ID获取/生成函数
// - qun(): 扩展激活主函数 (插件入口点)
// - ckt(): 上下文键设置函数
// - fkt(): 上下文键同步设置函数

// ============================================================================
// 插件架构和工作流程分析
// ============================================================================

/*
 * 插件的整体架构：
 *
 * 1. 扩展激活流程 (qun函数)：
 *    - 初始化日志记录器
 *    - 设置扩展启用/禁用处理器
 *    - 注册URI处理器 (OAuth回调、聊天打开等)
 *    - 创建核心服务实例
 *
 * 2. 核心服务组件：
 *    - 认证管理 (OAuth/API Token)
 *    - 配置管理 (用户设置)
 *    - 工作区管理 (文件同步、版本控制)
 *    - AI服务 (代码补全、聊天)
 *    - 特性标志管理
 *    - 事件报告和指标收集
 *
 * 3. 文件系统集成：
 *    - 文件监听和变更跟踪
 *    - 文件上传和同步
 *    - 路径过滤和忽略规则
 *    - Git集成和版本控制
 *
 * 4. UI集成：
 *    - WebView面板 (聊天界面)
 *    - 状态栏显示
 *    - 命令注册
 *    - 上下文菜单
 *
 * 5. AI功能：
 *    - 代码补全建议
 *    - 智能聊天对话
 *    - 代码编辑建议
 *    - 提交消息生成
 */

// ============================================================================
// 重要配置项和特性标志
// ============================================================================

/*
 * 从代码中提取的重要配置项：
 *
 * 基础功能配置：
 * - enableDebugFeatures: 启用调试功能
 * - enableReviewerWorkflows: 启用代码审查工作流
 * - enableUpload: 启用文件上传功能
 * - modelName: AI模型名称配置
 * - completionURL: 代码补全服务URL
 * - apiToken: API访问令牌
 * - oauth: OAuth认证配置
 *
 * 高级功能配置：
 * - enableNextEdit: 启用下一步编辑功能
 * - enableGenerateCommitMessage: 启用提交消息生成
 * - enableSmartPaste: 启用智能粘贴
 * - enableInstructions: 启用指令功能
 * - enableWorkspaceManagerUi: 启用工作区管理UI
 *
 * 同步和上传配置：
 * - maxUploadSizeBytes: 最大上传文件大小
 * - maxTrackableFiles: 最大可跟踪文件数
 * - enableFileLimitsForSyncingPermission: 启用同步权限文件限制
 * - refuseToSyncHomeDirectories: 拒绝同步主目录
 * - verifyFolderIsSourceRepo: 验证文件夹是源代码仓库
 *
 * 性能和限制配置：
 * - vscodeEnableCpuProfile: 启用CPU性能分析
 * - bypassLanguageFilter: 绕过语言过滤器
 * - enableCompletionFileEditEvents: 启用补全文件编辑事件
 *
 * 版本控制配置：
 * - vscodeSourcesMinVersion: VSCode源码最小版本
 * - vscodeChatHintDecorationMinVersion: 聊天提示装饰最小版本
 * - vscodeNextEditMinVersion: 下一步编辑最小版本
 * - vscodeGenerateCommitMessageMinVersion: 生成提交消息最小版本
 * - vscodeBackgroundAgentsMinVersion: 后台代理最小版本
 */

// ============================================================================
// 主要激活函数 qun() 详细分析
// ============================================================================

/*
 * qun() 函数是整个插件的入口点，相当于 activate() 函数
 *
 * 函数签名：function qun(r)
 * 参数 r: VSCode扩展上下文 (ExtensionContext)
 *
 * 主要执行流程：
 */

// 1. 初始化阶段
// - 创建日志记录器: let a = Ie("activate()")
// - 输出激活日志: a.debug("======== Activating extension ========")
// - 定义内部函数：
//   * e(e): 启用扩展函数 -> e.enable()
//   * t(e): 禁用扩展函数 -> e.disable()
//   * i(): 重新加载扩展函数 -> t(s), e(s)

// 2. 生命周期管理
// - 注册扩展销毁处理器: r.subscriptions.push(new Bt.Disposable(...))
// - 在扩展销毁时调用 t(s) 禁用扩展并清理资源

// 3. URI处理器注册
// - 注册URI处理器: Bt.window.registerUriHandler({handleUri(e) {...}})
// - 处理的URI路径包括：
//   * /auth/mcp: MCP OAuth回调处理
//   * S.authRedirectURI.path: 认证重定向URI
//   * zte.openChat: 打开聊天界面
//   * zte.openAugmentSettings: 打开Augment设置
//   * zte.openGuidelinesSettings: 打开指导原则设置
//   * zte.openMemories: 打开记忆文件

// 4. 系统信息收集
// - 收集平台信息: Mte.default.platform(), arch(), release()
// - 构建用户代理字符串: 包含扩展ID、版本、平台信息、VSCode版本

// 5. 核心服务初始化
// - 创建全局状态管理器: o = new JV(r)
// - 生成会话ID: l = j8e(o)
// - 创建配置监听器: c = new fH
// - 迁移旧配置: c.migrateLegacyConfig()
// - 创建认证管理器: u = new yH(r, c)
// - 创建同步启用跟踪器: d = new Vee

// 6. API和网络服务初始化
// - 创建API服务器: p = new dH(c, u, l, n, global.fetch)
// - 创建各种缓存和事件发射器:
//   * h = new kZ (完成缓存)
//   * f = new rA(10) (最近完成)
//   * g = new rA(10) (最近指令)
//   * m = new rA(10) (最近下一步编辑结果)
//   * _ = new Bt.EventEmitter (下一步编辑WebView事件)
//   * A = new Bt.EventEmitter (扩展更新事件)
//   * v = new Bt.EventEmitter (WebView应用变更事件)
//   * y = new Bt.EventEmitter (聊天扩展事件)

// 7. 工具和资源管理器初始化
// - 创建资产管理器: b = new fK(r)
// - 创建Git操作服务: w = new FN(new m8({defaultBackupDir: r.storageUri?.fsPath}))
// - 创建主面板提供器: E = new Nee(r.extensionUri)
// - 设置可见性变更处理: E.onVisibilityChange(...)

// 8. 状态和指标管理
// - 创建入职会话事件报告器: C = new BZ(o)
// - 创建客户端指标报告器: k = new JZ(p)
// - 创建工具配置存储: S = new tK(r, c, p, u, k)
// - 创建登录应用: I = new FZ(p, c, S, C)

// 9. 状态处理函数定义
// - O(e): 处理状态变更的函数
//   * "UserShouldSignIn": 用户需要登录
//   * "WorkspaceNotSelected": 工作区未选择
//   * "ShouldDisableCopilot": 应该禁用Copilot
//   * "ShouldDisableCodeium": 应该禁用Codium
//   * "SyncingPermissionNeeded": 需要同步权限
//   * "uploadingHomeDir": 正在上传主目录
//   * "workspaceTooLarge": 工作区太大

// - W(e): 焦点管理函数
//   * 如果未禁用自动焦点且面板不可见，则执行打开命令

// 10. 事件监听器注册
// - 会话变更监听: u.onDidChangeSession(() => i())
// - 状态管理器: new bH(C, u, c)
// - WebView提供器注册: Bt.window.registerWebviewViewProvider("augment-chat", E, ...)

// 11. 主扩展实例创建
// - 创建主扩展实例: s = new U9(r, o, c, p, u, h, f, m, g, _, y, E, v, C, d, A, k, b, w)
// - 这是整个插件的核心管理器，包含所有主要功能模块

// 12. 配置变更监听
// - 监听配置变更: c.onDidChange(e => {...})
// - 当关键配置项变更时重新加载扩展:
//   * apiToken: API令牌
//   * completionURL: 补全服务URL
//   * oauth: OAuth配置
//   * modelName: 模型名称

// 13. 最终注册和启用
// - 注册文本文档内容提供器: Bt.workspace.registerTextDocumentContentProvider(U9.contentScheme, s)
// - 执行额外初始化: Dft(r), fkt(s, c, r)
// - 创建并注册命令处理器: n = kCt(...)
// - 注册各种处理器: AH.register(r, c), mH.register(r, c, s)
// - 最终启用扩展: e(s)

// ============================================================================
// 核心扩展类 U9 分析
// ============================================================================

/*
 * U9 类是整个插件的核心管理器类，包含所有主要功能模块
 *
 * 构造函数参数分析：
 * - r: 扩展上下文 (ExtensionContext)
 * - o: 全局状态管理器 (GlobalState)
 * - c: 配置监听器 (ConfigListener)
 * - p: API服务器 (ApiServer)
 * - u: 认证管理器 (AuthManager)
 * - h: 完成缓存 (CompletionCache)
 * - f: 最近完成 (RecentCompletions)
 * - m: 最近指令 (RecentInstructions)
 * - g: 最近下一步编辑结果 (RecentNextEditResults)
 * - _: 下一步编辑WebView事件 (NextEditWebViewEvent)
 * - y: 聊天扩展事件 (ChatExtensionEvent)
 * - E: 主面板提供器 (MainPanelProvider)
 * - v: WebView应用变更事件 (ChangeWebviewAppEvent)
 * - C: 入职会话事件报告器 (OnboardingSessionEventReporter)
 * - d: 同步启用跟踪器 (SyncingEnabledTracker)
 * - A: 扩展更新事件 (ExtensionUpdateEvent)
 * - k: 客户端指标报告器 (ClientMetricsReporter)
 * - b: 资产管理器 (AssetManager)
 * - w: Git操作服务 (GitOperationsService)
 *
 * 主要功能模块：
 * - 状态栏管理: this._statusBar = new Wee
 * - 工作计时器: this.workTimer = new ZZ
 * - 特性标志管理器: this.featureFlagManager = new iH
 * - 完成接受报告器: this._completionAcceptanceReporter = new UZ
 * - 代码编辑报告器: this._codeEditReporter = new qZ
 * - 下一步编辑解决报告器: this._nextEditResolutionReporter = new jZ
 * - 下一步编辑会话事件报告器: this._nextEditSessionEventReporter = new KZ
 * - 下一步编辑配置管理器: this.nextEditConfigManager = new wK
 * - 客户端指标报告器: this._clientMetricsReporter = new QZ
 * - 完成时间线报告器: this._completionTimelineReporter = new VZ
 * - 扩展事件报告器: this._extensionEventReporter = new GZ
 * - 特征向量报告器: this._featureVectorReporter = new LZ
 * - 指导原则监听器: this.guidelinesWatcher = new Lg
 * - 规则监听器: this.rulesWatcher = new c4
 * - 工具使用请求事件报告器: this._toolUseRequestEventReporter = new $N
 * - 完成模型: this._completionsModel = new vZ
 */

// ============================================================================
// VSCode插件核心类和激活函数
// ============================================================================

/**
 * VSCode Augment插件主类 - U9
 * 这是整个插件的核心控制器，管理所有功能模块
 */
class AugmentExtension extends ze {  // U9 -> AugmentExtension, ze -> BaseClass
    constructor(extensionContext, globalState, configListener, apiServer, auth,
                recentCompletions, recentInstructions, recentNextEditResults,
                recentChats, nextEditWebViewEvent, onExtensionUpdateEvent,
                mainPanelProvider, changeWebviewAppEvent, actionsModel,
                syncingEnabledTracker, chatExtensionEvent, onboardingSessionEventReporter,
                assetManager, gitOperationsService) {

        super();

        // 核心上下文和状态管理
        this._extensionContext = extensionContext;           // VSCode扩展上下文
        this._globalState = globalState;                     // 全局状态存储
        this._augmentConfigListener = configListener;       // 配置监听器
        this._apiServer = apiServer;                         // API服务器
        this._auth = auth;                                   // 认证管理器

        // 最近活动记录
        this._recentCompletions = recentCompletions;         // 最近的代码补全
        this._recentInstructions = recentInstructions;       // 最近的指令
        this._recentNextEditResults = recentNextEditResults; // 最近的下一步编辑结果
        this._recentChats = recentChats;                     // 最近的聊天记录

        // 事件发射器
        this._nextEditWebViewEvent = nextEditWebViewEvent;   // 下一步编辑WebView事件
        this._onExtensionUpdateEvent = onExtensionUpdateEvent; // 扩展更新事件
        this._changeWebviewAppEvent = changeWebviewAppEvent; // WebView应用变更事件
        this._chatExtensionEvent = chatExtensionEvent;       // 聊天扩展事件

        // UI和状态管理
        this._mainPanelProvider = mainPanelProvider;         // 主面板提供器
        this._actionsModel = actionsModel;                   // 操作模型
        this._syncingEnabledTracker = syncingEnabledTracker; // 同步启用跟踪器
        this._onboardingSessionEventReporter = onboardingSessionEventReporter; // 入职会话事件报告器
        this._assetManager = assetManager;                   // 资产管理器
        this._gitOperationsService = gitOperationsService;   // Git操作服务

        // 初始化核心组件
        this._statusBar = new StatusBarManager();           // Wee -> StatusBarManager
        extensionContext.subscriptions.push(this._statusBar);

        this.workTimer = new WorkTimer();                   // ZZ -> WorkTimer

        // 特性标志管理器
        this.featureFlagManager = new FeatureFlagManager({  // iH -> FeatureFlagManager
            fetcher: this._fetchFeatureFlags.bind(this),
            refreshIntervalMSec: 1800000  // 30分钟
        }, this._augmentConfigListener);

        // 各种事件报告器
        this._completionAcceptanceReporter = new CompletionAcceptanceReporter(apiServer, this._onboardingSessionEventReporter); // UZ -> CompletionAcceptanceReporter
        this._codeEditReporter = new CodeEditReporter(apiServer);                    // qZ -> CodeEditReporter
        this._nextEditResolutionReporter = new NextEditResolutionReporter(apiServer); // jZ -> NextEditResolutionReporter
        this._nextEditSessionEventReporter = new NextEditSessionEventReporter(apiServer); // KZ -> NextEditSessionEventReporter

        // 配置管理器
        this.nextEditConfigManager = new NextEditConfigManager(               // wK -> NextEditConfigManager
            this._augmentConfigListener,
            this.featureFlagManager,
            this._globalState
        );

        // 更多报告器
        this._clientMetricsReporter = new ClientMetricsReporter(apiServer);   // QZ -> ClientMetricsReporter
        this._completionTimelineReporter = new CompletionTimelineReporter(apiServer); // VZ -> CompletionTimelineReporter
        this._extensionEventReporter = new ExtensionEventReporter(apiServer); // GZ -> ExtensionEventReporter
        this._featureVectorReporter = new FeatureVectorReporter(apiServer, extensionContext); // LZ -> FeatureVectorReporter

        // 文件监听器
        this.guidelinesWatcher = new GuidelinesWatcher(                       // Lg -> GuidelinesWatcher
            this._augmentConfigListener,
            this.featureFlagManager,
            this._clientMetricsReporter
        );
        this.rulesWatcher = new RulesWatcher(this.workspaceManager);          // c4 -> RulesWatcher

        // 工具使用请求事件报告器
        this._toolUseRequestEventReporter = new ToolUseRequestEventReporter(); // $N -> ToolUseRequestEventReporter

        // 添加到销毁列表
        this.disposeOnDisable.push(this.guidelinesWatcher);
        this.disposeOnDisable.push(this.rulesWatcher);
        this.addDisposable(new Bt.Disposable(() => this.disable()));

        // 完成模型
        this._completionsModel = new CompletionsModel(                        // vZ -> CompletionsModel
            this,
            this._augmentConfigListener,
            this._clientMetricsReporter
        );

        // macOS证书处理
        if (!isVSCodeVersionAtLeast("1.96.0")) {  // qo -> isVSCodeVersionAtLeast
            try {
                this._logger.info("Starting macCA");
                addToGlobalAgent();  // pkt.addToGlobalAgent -> addToGlobalAgent
                this._logger.info("macCa Done");
            } catch (error) {
                this._logger.error("Exception loading mac-ca certs:", error);
            }
        }
    }

    // 静态属性
    static augmentRootName = ".augmentroot";
    static contentScheme = "augment";
    static displayStatusUri = Bt.Uri.from({
        scheme: this.contentScheme,
        path: "Augment Extension Status"
    });
    static modelConfigBackoffMsecMax = 30000; // 30秒

    // 实例属性
    keybindingWatcher = undefined;
    _completionServer = undefined;
    workspaceManager = undefined;

    // 更多核心属性
    _modelInfo = undefined;                          // 模型信息
    _blobNameCalculator = undefined;                 // Blob名称计算器
    _suggestionManager = undefined;                  // 建议管理器
    _nextEditRequestManager = undefined;             // 下一步编辑请求管理器
    _editorNextEdit = undefined;                     // 编辑器下一步编辑
    _backgroundNextEdit = undefined;                 // 后台下一步编辑
    _globalNextEdit = undefined;                     // 全局下一步编辑
    _diagnosticsManager = undefined;                 // 诊断管理器
    _nextEditVSCodeToWebviewMessage = new Bt.EventEmitter(); // VSCode到WebView消息事件发射器
    _openChatHintManager = undefined;                // 打开聊天提示管理器
    _remoteWorkspaceResolver = undefined;            // 远程工作区解析器
    _analyticsManager = undefined;                   // 分析管理器
    _inlineCompletionProvider = undefined;           // 内联补全提供器
    _chatModel = undefined;                          // 聊天模型
    _currentChatExtensionEventDisposable = undefined; // 当前聊天扩展事件可销毁对象
    _notificationWatcher = undefined;                // 通知监听器
    _toolsModel = undefined;                         // 工具模型
    _taskManager = undefined;                        // 任务管理器
    _exchangeManager = undefined;                    // 交换管理器
    _toolUseStateManager = undefined;                // 工具使用状态管理器
    _agentCheckpointManager = undefined;             // 代理检查点管理器
    _toolConfigStore = undefined;                    // 工具配置存储
    toolApprovalConfigManager = undefined;           // 工具批准配置管理器
    syncingStatusReporter = undefined;               // 同步状态报告器
    fuzzyFsSearcher = undefined;                     // 模糊文件系统搜索器
    fuzzySymbolSearcher = undefined;                 // 模糊符号搜索器

    // 状态属性
    enabled = false;                                 // 是否已启用
    enableInProgress = false;                        // 是否正在启用中
    disposeOnDisable = [];                          // 禁用时需要销毁的对象列表
    _enableCancel = undefined;                       // 启用取消令牌
    _statusTrace = undefined;                        // 状态跟踪
    _dataCollector = undefined;                      // 数据收集器
    _logger = createLogger("AugmentExtension");      // Ie -> createLogger

    // 获取会话ID
    get sessionId() {
        return this._apiServer.sessionId;
    }

    // 获取是否准备就绪
    get ready() {
        return this.enabled && this._modelInfo !== undefined;
    }

    // 获取可用模型列表
    get _availableModels() {
        return this._modelInfo?.models?.map(model => model.name) || [];
    }

    // 获取默认模型
    get _defaultModel() {
        return this._modelInfo?.defaultModel;
    }

    // 获取支持的语言列表
    get _languages() {
        return this._modelInfo?.languages || [];
    }
}

// ============================================================================
// VSCode插件激活函数
// ============================================================================

/**
 * VSCode插件激活函数 - qun
 * 这是VSCode插件的主入口点，当插件被激活时调用
 *
 * @param {vscode.ExtensionContext} extensionContext - VSCode扩展上下文
 */
function activateAugmentExtension(extensionContext) {  // qun -> activateAugmentExtension
    let logger = createLogger("activate()");  // Ie -> createLogger
    logger.debug("======== Activating extension ========");

    let augmentExtension; // s -> augmentExtension

    // 启用扩展
    function enableExtension(extension) {  // e -> enableExtension
        extension.enable();
    }

    // 禁用扩展
    function disableExtension(extension) {  // t -> disableExtension
        extension.disable();
    }

    // 重新加载扩展
    function reloadExtension() {  // i -> reloadExtension
        logger.info("======== Reloading extension ========");
        disableExtension(augmentExtension);
        enableExtension(augmentExtension);
    }

    // 注册扩展销毁处理器
    extensionContext.subscriptions.push(new Bt.Disposable(() => {
        if (augmentExtension) {
            logger.debug("======== Deactivating extension ========");
            disableExtension(augmentExtension);
        }
        augmentExtension = undefined;
    }));

    // 注册URI处理器
    extensionContext.subscriptions.push(Bt.window.registerUriHandler({
        handleUri(uri) {
            // 检查URI权限
            if (uri.authority.toLowerCase() !== extensionContext.extension.id.toLowerCase()) {
                logger.warn("Ignoring URI " + uri.toString());
                return;
            }

            // 处理MCP OAuth回调
            if (uri.path.startsWith("/auth/mcp")) {
                logger.info("Detected MCP OAuth callback, routing to handler");
                handleMCPOAuthCallback(uri, augmentExtension?.toolsModel, extensionContext, augmentExtension?.toolConfigStore); // qAt -> handleMCPOAuthCallback
                return;
            }

            // 处理其他URI路径
            switch (uri.path) {
                case AuthRedirectURIHandler.authRedirectURI.path:  // S.authRedirectURI.path
                    AuthRedirectURIHandler.handleAuthURI(uri);     // S.handleAuthURI
                    break;

                case URIActions.openChat:  // zte.openChat
                    var mode = new URLSearchParams(uri.query).get("mode");
                    if (mode && !["agent", "chat"].includes(mode)) {
                        logger.error("Invalid chat mode: " + mode);
                    } else {
                        Bt.commands.executeCommand(OpenChatCommand.commandID, mode); // zC.commandID
                    }
                    break;

                case URIActions.openAugmentSettings:  // zte.openAugmentSettings
                    Bt.commands.executeCommand(OpenSettingsCommand.commandID);     // H9.commandID
                    break;

                case URIActions.openGuidelinesSettings:  // zte.openGuidelinesSettings
                    Bt.commands.executeCommand(OpenSettingsCommand.commandID, "userGuidelines"); // H9.commandID
                    break;

                case URIActions.openMemories:  // zte.openMemories
                    var memoriesPath = augmentExtension?.toolsModel?.memoriesAbsPath;
                    if (memoriesPath) {
                        var memoriesUri = Bt.Uri.file(memoriesPath);
                        Bt.commands.executeCommand("vscode.open", memoriesUri);
                    } else {
                        logger.warn("Could not open memories: path not found.");
                    }
                    break;

                default:
                    logger.error("Unhandled URI " + Bt.Uri.from({
                        scheme: uri.scheme,
                        authority: uri.authority,
                        path: uri.path
                    }).toString());
            }
        }
    }));

    // 构建用户代理字符串
    var platformInfo = `${os.platform()}; ${os.arch()}; ${os.release()}`;  // Mte.default -> os
    var userAgent = `${extensionContext.extension.id}/${extensionContext.extension.packageJSON.version} (${platformInfo}) ${Bt.env.uriScheme}/${Bt.version}`;

    // 创建核心组件
    var globalState = new GlobalStateManager(extensionContext);  // JV -> GlobalStateManager
    extensionContext.subscriptions.push(globalState);

    let sessionId = generateSessionId(globalState);  // j8e -> generateSessionId
    let configListener = new ConfigurationListener(); // fH -> ConfigurationListener
    configListener.migrateLegacyConfig();

    var authManager = new AuthenticationManager(extensionContext, configListener); // yH -> AuthenticationManager
    extensionContext.subscriptions.push(authManager);

    var statusReporter = new StatusReporter(); // Vee -> StatusReporter
    extensionContext.subscriptions.push(statusReporter);

    // 初始化配置管理器
    initializeConfigManager(new ConfigManager(configListener)); // qTe, mK -> initializeConfigManager, ConfigManager
    initializeGlobalConfigManager(new GlobalConfigManager(authManager, configListener)); // $Te, gK -> initializeGlobalConfigManager, GlobalConfigManager

    // 创建API服务器
    let apiServer = new APIServer(configListener, authManager, sessionId, userAgent, global.fetch); // dH -> APIServer
    let statusBar = new StatusBarManager(); // kZ -> StatusBarManager

    // 创建最近活动记录器
    let recentCompletions = new RecentActivityTracker(10);    // rA -> RecentActivityTracker
    let recentInstructions = new RecentActivityTracker(10);
    let recentNextEditResults = new RecentActivityTracker(10);

    // 创建事件发射器
    let nextEditWebViewEvent = new Bt.EventEmitter();
    let onExtensionUpdateEvent = new Bt.EventEmitter();
    let changeWebviewAppEvent = new Bt.EventEmitter();
    let chatExtensionEvent = new Bt.EventEmitter();

    // 创建主面板提供器
    let mainPanelProvider = new MainPanelProvider(extensionContext); // fK -> MainPanelProvider

    // 创建任务管理器
    let taskManager = new TaskManager(new TaskManagerConfig({  // FN, m8 -> TaskManager, TaskManagerConfig
        defaultBackupDir: extensionContext.storageUri?.fsPath
    }));

    // 创建资产管理器
    let assetManager = new AssetManager(extensionContext.extensionUri); // Nee -> AssetManager
    assetManager.onVisibilityChange(isVisible => {
        if (!isVisible) {
            ChatPanel.currentPanel?.dispose(); // mC -> ChatPanel
        }
    });

    // 创建操作模型
    var actionsModel = new ActionsModel(globalState); // BZ -> ActionsModel
    extensionContext.subscriptions.push(actionsModel);

    // 创建同步启用跟踪器
    var syncingEnabledTracker = new SyncingEnabledTracker(apiServer); // JZ -> SyncingEnabledTracker

    // 创建认证状态管理器
    var authStateManager = new AuthenticationStateManager(apiServer, configListener, syncingEnabledTracker); // tK -> AuthenticationStateManager

    // 创建入职会话事件报告器
    var onboardingSessionEventReporter = new OnboardingSessionEventReporter(apiServer, configListener, authStateManager, actionsModel); // FZ -> OnboardingSessionEventReporter

    // 状态处理函数
    function handleSystemStates(states) {  // O -> handleSystemStates
        stateLoop: for (var state of states) {
            switch (state.name) {
                case "UserShouldSignIn":
                    assetManager.changeApp(onboardingSessionEventReporter);
                    showPanelIfNeeded(configListener.config);
                    break stateLoop;
                case "WorkspaceNotSelected":
                    if (actionsModel.isSystemStateComplete("authenticated")) {
                        changeWebviewAppEvent.fire("folder-selection");
                    }
                    break stateLoop;
                case "ShouldDisableCopilot":
                case "ShouldDisableCodeium":
                case "SyncingPermissionNeeded":
                case "uploadingHomeDir":
                case "workspaceTooLarge":
                    showPanelIfNeeded(configListener.config);
                    break stateLoop;
            }
        }

        // 检查是否需要同步权限
        if (actionsModel.isDerivedStateSatisfied("SyncingPermissionNeeded") ||
            actionsModel.isDerivedStateSatisfied("uploadingHomeDir") ||
            actionsModel.isDerivedStateSatisfied("workspaceTooLarge")) {
            changeWebviewAppEvent.fire("awaiting-syncing-permission");
        }
    }

    // 显示面板函数
    function showPanelIfNeeded(config) {  // W -> showPanelIfNeeded
        if (!config.disableFocusOnAugmentPanel && !assetManager.isVisible()) {
            Bt.commands.executeCommand(ShowPanelCommand.commandID); // uo.commandID
        }
    }

    // 注册会话变更监听器
    extensionContext.subscriptions.push(authManager.onDidChangeSession(() => {
        reloadExtension();
    }));

    // 注册状态监听器
    extensionContext.subscriptions.push(new StateListener(actionsModel, authManager, configListener)); // bH -> StateListener

    // 注册WebView提供器
    extensionContext.subscriptions.push(Bt.window.registerWebviewViewProvider("augment-chat", assetManager, {
        webviewOptions: {
            retainContextWhenHidden: true
        }
    }));

    // 创建主扩展实例
    augmentExtension = new AugmentExtension(  // U9 -> AugmentExtension
        extensionContext,
        globalState,
        configListener,
        apiServer,
        authManager,
        statusBar,
        recentCompletions,
        recentNextEditResults,
        recentInstructions,
        nextEditWebViewEvent,
        chatExtensionEvent,
        assetManager,
        changeWebviewAppEvent,
        actionsModel,
        statusReporter,
        onExtensionUpdateEvent,
        syncingEnabledTracker,
        mainPanelProvider,
        taskManager
    );

    // 注册状态监听器
    extensionContext.subscriptions.push(actionsModel.onDerivedStatesSatisfied(handleSystemStates));
    handleSystemStates(actionsModel.satisfiedStates);

    // 监听配置变更
    configListener.onDidChange(configChange => {
        // 检查调试功能是否变更
        configChange.newConfig.enableDebugFeatures;
        configChange.previousConfig.enableDebugFeatures;
    });

    // 监听重要配置变更并重新加载扩展
    extensionContext.subscriptions.push(configListener.onDidChange(configChange => {
        let shouldReload = false;

        // 检查关键配置项是否变更
        for (var configKey of ["apiToken", "completionURL", "oauth", "modelName"]) {
            if (!isEqual(configChange.previousConfig[configKey], configChange.newConfig[configKey])) { // G8e.default -> isEqual
                shouldReload = true;
                break;
            }
        }

        if (shouldReload) {
            logger.info("Reloading extension due to config change");
            reloadExtension();
        }
    }));

    // 注册内容提供器
    extensionContext.subscriptions.push(Bt.workspace.registerTextDocumentContentProvider(AugmentExtension.contentScheme, augmentExtension));

    // 初始化其他功能
    initializeDebugFeatures(extensionContext); // Dft -> initializeDebugFeatures
    setupContextKeySync(augmentExtension, configListener, extensionContext); // fkt -> setupContextKeySync

    // 注册命令和功能
    var commandRegistrations = registerCommands(  // kCt -> registerCommands
        extensionContext,
        augmentExtension,
        configListener,
        authManager,
        authStateManager,
        apiServer,
        statusBar,
        recentCompletions,
        recentNextEditResults,
        changeWebviewAppEvent,
        onExtensionUpdateEvent,
        statusReporter,
        globalState,
        extensionContext.workspaceState
    );
    extensionContext.subscriptions.push(commandRegistrations);

    // 注册事件发射器
    extensionContext.subscriptions.push(chatExtensionEvent);

    // 注册其他功能
    extensionContext.subscriptions.push(AnalyticsHandler.register(extensionContext, configListener)); // AH -> AnalyticsHandler
    extensionContext.subscriptions.push(ModelInfoHandler.register(extensionContext, configListener, augmentExtension)); // mH -> ModelInfoHandler

    // 启用扩展
    enableExtension(augmentExtension);
}

// ============================================================================
// 上下文键同步功能
// ============================================================================

/**
 * 设置VSCode上下文键
 * @param {Object} contextKeys - 上下文键对象
 */
function setContextKeys(contextKeys) {  // ckt -> setContextKeys
    for (var [key, value] of Object.entries(contextKeys)) {
        Bt.commands.executeCommand("setContext", key, value);
    }
}

/**
 * 设置上下文键同步
 * @param {AugmentExtension} extension - 扩展实例
 * @param {ConfigurationListener} configListener - 配置监听器
 * @param {vscode.ExtensionContext} extensionContext - 扩展上下文
 */
function setupContextKeySync(extension, configListener, extensionContext) {  // fkt -> setupContextKeySync
    // 基础上下文键更新函数
    var updateBasicContextKeys = () => {  // a -> updateBasicContextKeys
        var config = configListener.config;
        setContextKeys({
            "vscode-augment.enableDebugFeatures": config.enableDebugFeatures,
            "vscode-augment.enableReviewerWorkflows": config.enableReviewerWorkflows,
            "vscode-augment.enableNextEdit": isNextEditEnabled(configListener.config, extension?.featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? ""), // kf -> isNextEditEnabled
            "vscode-augment.enableNextEditBackgroundSuggestions": isNextEditBackgroundEnabled(configListener.config, extension?.featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? ""), // vO -> isNextEditBackgroundEnabled
            "vscode-augment.enableGenerateCommitMessage": isVersionAtLeast(extension?.featureFlagManager.currentFlags.vscodeGenerateCommitMessageMinVersion ?? ""), // or -> isVersionAtLeast
            "vscode-augment.nextEdit.enablePanel": extension.nextEditConfigManager.config.enablePanel,
            "vscode-augment.featureFlags.enableRemoteAgents": isVersionAtLeast(extension?.featureFlagManager.currentFlags.vscodeBackgroundAgentsMinVersion ?? "") ?? false
        });
    };

    // 初始化基础上下文键
    updateBasicContextKeys();
    extensionContext.subscriptions.push(configListener.onDidChange(updateBasicContextKeys));

    // 特性标志相关的上下文键
    var featureFlagKeys = [  // s -> featureFlagKeys
        "enableWorkspaceManagerUi",
        "enableSmartPaste",
        "enableSmartPasteMinVersion",
        "enableInstructions",
        "vscodeSourcesMinVersion",
        "vscodeChatHintDecorationMinVersion",
        "vscodeEnableCpuProfile",
        "vscodeNextEditMinVersion",
        "vscodeGenerateCommitMessageMinVersion"
    ];

    // 特性标志上下文键更新函数
    var updateFeatureFlagContextKeys = () => {  // i -> updateFeatureFlagContextKeys
        if (extension) {
            var flags = extension.featureFlagManager.currentFlags;
            setContextKeys({
                "vscode-augment.workspace-manager-ui.enabled": flags.enableWorkspaceManagerUi,
                "vscode-augment.internal-new-instructions.enabled": flags.enableInstructions,
                "vscode-augment.internal-dv.enabled": isVersionAtLeast(flags.enableSmartPasteMinVersion) || flags.enableInstructions,
                "vscode-augment.sources-enabled": isVersionAtLeast(flags.vscodeSourcesMinVersion) ?? false,
                "vscode-augment.chat-hint.decoration": isVersionAtLeast(flags.vscodeChatHintDecorationMinVersion) ?? false,
                "vscode-augment.cpu-profile.enabled": flags.vscodeEnableCpuProfile,
                "vscode-augment.enableGenerateCommitMessage": isVersionAtLeast(flags.vscodeGenerateCommitMessageMinVersion) ?? false,
                "vscode-augment.featureFlags.enableRemoteAgents": isVersionAtLeast(flags.vscodeBackgroundAgentsMinVersion) ?? false,
                "vscode-augment.nextEdit.enablePanel": extension.nextEditConfigManager.config.enablePanel
            });
        }
    };

    // 初始化特性标志上下文键
    updateFeatureFlagContextKeys();
    extensionContext.subscriptions.push(extension.featureFlagManager.subscribe(featureFlagKeys, updateFeatureFlagContextKeys));
    extensionContext.subscriptions.push(extension.featureFlagManager.subscribe(featureFlagKeys, updateBasicContextKeys));
}

// 导出上下文键同步功能
var ContextKeySync = {  // Uun -> ContextKeySync
    setupContextKeySync: setupContextKeySync
};
